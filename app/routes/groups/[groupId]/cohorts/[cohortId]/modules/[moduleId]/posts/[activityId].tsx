import { useEffect, useState, useCallback } from "react";
import { use<PERSON>ara<PERSON>, Link } from "react-router";
import { Loader2, Arrow<PERSON>ef<PERSON>, Check } from "lucide-react";

import type { Route } from "./+types/[activityId]";
import { useAppContext } from "~/lib/providers/app-context";
import { useProfile, useMyCohortModule } from "~/lib/api/client-queries";
import {
  FeedPostCard,
  type EnrichedActivityWithText,
} from "~/components/feed/FeedPostCard";
import { ImageViewer } from "~/components/feed/ImageViewer";

type CommentWithUser = EnrichedReaction & {
  user?: {
    id: string;
    created_at: string;
    updated_at: string;
    data: {
      name?: string;
      image?: string;
    };
  };
};

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Post - Sphere` },
    { name: "description", content: "Individual post view" },
  ];
}

export default function PostDetailPage() {
  const { groupId, cohortId, moduleId, activityId } = useParams();
  const { userId, streamClient } = useAppContext();
  const { data: profileData } = useProfile();

  // Get module data to access feed configuration
  const {
    data: moduleResponse,
    isLoading: moduleLoading,
    isError: moduleError,
  } = useMyCohortModule(groupId!, cohortId!, moduleId!);

  const [activity, setActivity] = useState<EnrichedActivityWithText | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [comments, setComments] = useState<CommentWithUser[]>([]);
  const [commentInput, setCommentInput] = useState("");
  const [loadingComments, setLoadingComments] = useState(false);
  const [shareDropdownOpen, setShareDropdownOpen] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [imageViewerOpen, setImageViewerOpen] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string>("");
  const [selectedImageAlt, setSelectedImageAlt] = useState<string>("");

  // Fetch individual activity
  useEffect(() => {
    const fetchActivity = async () => {
      if (!streamClient || !moduleResponse?.data || moduleLoading) return;

      try {
        setLoading(true);
        setError(null);

        const module = moduleResponse.data;

        // Get the feed using the config from the module
        const feed = streamClient.feed(
          module.config.feedGroup,
          module.config.feedId
        );

        // Fetch the specific activity
        const response = await feed.get({
          limit: 1,
          id_lte: activityId,
          id_gte: activityId,
          withReactionCounts: true,
          withOwnReactions: true,
          enrich: true,
        });

        if (response.results.length === 0) {
          setError("Post not found");
          return;
        }

        const activityData = response.results[0] as EnrichedActivityWithText;
        setActivity(activityData);

        // Fetch comments for this activity
        setLoadingComments(true);
        try {
          const commentsResponse = await streamClient.reactions.filter({
            activity_id: activityId!,
            kind: "comment",
            limit: 50,
          });

          setComments(commentsResponse.results as CommentWithUser[]);
        } catch (commentsError) {
          console.error("Error fetching comments:", commentsError);
        } finally {
          setLoadingComments(false);
        }
      } catch (err) {
        console.error("Error fetching activity:", err);
        setError("Failed to load post. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchActivity();
  }, [streamClient, moduleResponse, moduleLoading, activityId]);

  // Close share dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShareDropdownOpen(false);
    };

    if (shareDropdownOpen) {
      document.addEventListener("click", handleClickOutside);
      return () => {
        document.removeEventListener("click", handleClickOutside);
      };
    }
  }, [shareDropdownOpen]);

  const handleLike = async () => {
    if (!streamClient || !activity) return;

    try {
      const reaction = await streamClient.reactions.add("like", activity.id);

      setActivity({
        ...activity,
        own_reactions: {
          ...activity.own_reactions,
          like: [...(activity.own_reactions?.like || []), reaction],
        },
        reaction_counts: {
          ...activity.reaction_counts,
          like: (activity.reaction_counts?.like || 0) + 1,
        },
      });
    } catch (error) {
      console.error("Error adding like:", error);
    }
  };

  const handleUnlike = async (reactionId: string) => {
    if (!streamClient || !activity) return;

    try {
      await streamClient.reactions.delete(reactionId);

      setActivity({
        ...activity,
        own_reactions: {
          ...activity.own_reactions,
          like: activity.own_reactions?.like?.filter(
            (r) => r.id !== reactionId
          ),
        },
        reaction_counts: {
          ...activity.reaction_counts,
          like: Math.max((activity.reaction_counts?.like || 0) - 1, 0),
        },
      });
    } catch (error) {
      console.error("Error removing like:", error);
    }
  };

  const handleAddComment = async () => {
    if (!streamClient || !commentInput.trim() || !activity) return;

    try {
      const comment = await streamClient.reactions.add("comment", activity.id, {
        text: commentInput,
      });

      // Add comment to local state with proper user data structure
      const newComment: CommentWithUser = {
        ...comment,
        user: {
          id: userId!,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          data: {
            name:
              profileData?.data?.firstName && profileData?.data?.lastName
                ? `${profileData.data.firstName} ${profileData.data.lastName}`.trim()
                : profileData?.data?.firstName ||
                  profileData?.data?.lastName ||
                  "Anonymous",
          },
        },
      };

      setComments([newComment, ...comments]);

      // Update activity comment count
      setActivity({
        ...activity,
        reaction_counts: {
          ...activity.reaction_counts,
          comment: (activity.reaction_counts?.comment || 0) + 1,
        },
      });

      // Clear input
      setCommentInput("");
    } catch (error) {
      console.error("Error adding comment:", error);
    }
  };

  const handleShare = async () => {
    const postUrl = window.location.href;

    try {
      await navigator.clipboard.writeText(postUrl);
      setShowToast(true);
      setTimeout(() => setShowToast(false), 3000);
      console.log("Link copied to clipboard!");
    } catch (err) {
      // Fallback for older browsers
      try {
        const textArea = document.createElement("textarea");
        textArea.value = postUrl;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        setShowToast(true);
        setTimeout(() => setShowToast(false), 3000);
        console.log("Link copied to clipboard (fallback)!");
      } catch (fallbackErr) {
        console.error("Failed to copy link:", fallbackErr);
      }
    }
  };

  const handleImageClick = useCallback((imageUrl: string, alt: string) => {
    setSelectedImageUrl(imageUrl);
    setSelectedImageAlt(alt);
    setImageViewerOpen(true);
  }, []);

  const handleCloseImageViewer = useCallback(() => {
    setImageViewerOpen(false);
    setSelectedImageUrl("");
    setSelectedImageAlt("");
  }, []);

  const handleCommentFromCard = useCallback(
    async (activityId: string, text: string) => {
      if (!streamClient || !text.trim() || !activity) return;

      try {
        const comment = await streamClient.reactions.add(
          "comment",
          activityId,
          {
            text: text,
          }
        );

        // Add comment to local state with proper user data structure
        const newComment: CommentWithUser = {
          ...comment,
          user: {
            id: userId!,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            data: {
              name:
                profileData?.data?.firstName && profileData?.data?.lastName
                  ? `${profileData.data.firstName} ${profileData.data.lastName}`.trim()
                  : profileData?.data?.firstName ||
                    profileData?.data?.lastName ||
                    "Anonymous",
            },
          },
        };

        setComments([newComment, ...comments]);

        // Update activity comment count
        setActivity({
          ...activity,
          reaction_counts: {
            ...activity.reaction_counts,
            comment: (activity.reaction_counts?.comment || 0) + 1,
          },
        });
      } catch (error) {
        console.error("Error adding comment:", error);
      }
    },
    [streamClient, activity, userId, profileData, comments]
  );

  if (moduleLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (moduleError || error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black">
        <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-300 mb-4">
          {error || "Failed to load post"}
        </div>
        <Link
          to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
          className="text-blue-400 hover:text-blue-300 flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Feed
        </Link>
      </div>
    );
  }

  if (!activity) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black">
        <p className="text-zinc-400 mb-4">Post not found</p>
        <Link
          to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
          className="text-blue-400 hover:text-blue-300 flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Feed
        </Link>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-black">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-900">
        <div className="max-w-4xl mx-auto px-8 py-4">
          <div className="flex items-center gap-4">
            <Link
              to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
              className="text-zinc-400 hover:text-white flex items-center gap-2 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              Back to Feed
            </Link>
          </div>
        </div>
      </div>

      {/* Post Content */}
      <div className="flex-1 overflow-auto bg-black">
        <div className="max-w-4xl mx-auto px-8 py-12">
          <FeedPostCard
            activity={activity}
            onLike={(activityId, isLiked, reactionId) => {
              if (isLiked && reactionId) {
                handleUnlike(reactionId);
              } else {
                handleLike();
              }
            }}
            onComment={handleCommentFromCard}
            onShare={handleShare}
            onImageClick={handleImageClick}
            showComments={false}
            isClickable={false}
          />

          {/* Comments Section */}
          <div className="mt-4 pt-4">
            <h3 className="text-lg font-semibold text-white mb-4">Comments</h3>

            {/* Comment Input */}
            <div className="flex gap-2 mb-6">
              <input
                type="text"
                value={commentInput}
                onChange={(e) => setCommentInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleAddComment();
                  }
                }}
                placeholder="Write a comment..."
                className="flex-1 px-3 py-2 text-white bg-zinc-700 border border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-zinc-400"
              />
              <button
                onClick={handleAddComment}
                disabled={!commentInput.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>

            {/* Comments List */}
            {loadingComments ? (
              <div className="flex justify-center py-4">
                <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
              </div>
            ) : (
              <div className="space-y-4">
                {comments.map((comment) => (
                  <div key={comment.id} className="flex gap-3">
                    {comment.user?.data?.image ? (
                      <img
                        src={comment.user.data.image}
                        alt={comment.user.data.name || "User"}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
                        <span className="text-zinc-300 text-sm font-medium">
                          {(comment.user?.data?.name || "U")[0].toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="bg-zinc-700 rounded-lg px-3 py-2">
                        <p className="text-sm font-medium text-white">
                          {comment.user?.data?.name || "Anonymous"}
                        </p>
                        <p className="text-sm text-zinc-300">
                          {(comment.data as any)?.text || comment.data}
                        </p>
                      </div>
                      <p className="text-xs text-zinc-500 mt-1">
                        {formatTime(comment.created_at)}
                      </p>
                    </div>
                  </div>
                ))}
                {comments.length === 0 && (
                  <p className="text-sm text-zinc-400 text-center py-4">
                    No comments yet. Be the first to comment!
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 bg-zinc-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <Check className="w-4 h-4" />
          Link copied to clipboard!
        </div>
      )}

      {/* Image Viewer Modal */}
      <ImageViewer
        imageUrl={selectedImageUrl}
        alt={selectedImageAlt}
        isOpen={imageViewerOpen}
        onClose={handleCloseImageViewer}
      />
    </div>
  );
}
