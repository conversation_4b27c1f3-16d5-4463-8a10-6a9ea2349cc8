import { useState, useCallback, useRef, memo, useEffect } from "react";
import { type EnrichedActivity, type EnrichedReaction } from "getstream";
import { useNavigate } from "react-router";
import {
  Loader2,
  MessageSquare,
  Heart,
  Share2,
  MoreHorizontal,
  Send,
  ImageIcon,
  X,
  Link,
  Check,
  ZoomIn,
} from "lucide-react";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import type { FeedModule as FeedModuleType } from "~/lib/api/types";
import {
  useProfile,
  useGetUploadUrl,
  useFeedActivities,
  useFeedRealtime,
  useFeedLike,
  useFeedComment,
  useFeedPost,
  useFeedComments,
} from "~/lib/api/client-queries";
import { canUserPostToFeed } from "~/lib/utils/feed";

// Initialize dayjs plugins at module level (performance optimization)
dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(timezone);

// Helper function to check if a file is an image
const isImageFile = (file: File): boolean => {
  return file.type.startsWith("image/");
};

// Image Viewer Modal Component
interface ImageViewerProps {
  imageUrl: string;
  alt: string;
  isOpen: boolean;
  onClose: () => void;
}

const ImageViewer = memo(
  ({ imageUrl, alt, isOpen, onClose }: ImageViewerProps) => {
    useEffect(() => {
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
          onClose();
        }
      };

      if (isOpen) {
        document.addEventListener("keydown", handleEscape);
        document.body.style.overflow = "hidden";
      }

      return () => {
        document.removeEventListener("keydown", handleEscape);
        document.body.style.overflow = "unset";
      };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    return (
      <div
        className="fixed inset-0 bg-black/90 backdrop-blur-sm flex items-center justify-center z-50"
        onClick={onClose}
      >
        <div className="relative max-w-[90vw] max-h-[90vh] flex items-center justify-center">
          <img
            src={imageUrl}
            alt={alt}
            className="max-w-full max-h-full object-contain rounded-lg"
            onClick={(e) => e.stopPropagation()}
          />
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
          >
            <X className="w-6 h-6 text-white" />
          </button>
        </div>
      </div>
    );
  }
);

ImageViewer.displayName = "ImageViewer";

interface FeedModuleProps {
  module: FeedModuleType;
  params: {
    groupId: string;
    cohortId: string;
    moduleId: string;
  };
}

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  own_reactions?: {
    like?: EnrichedReaction[];
    comment?: EnrichedReaction[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

// Memoized ActivityCard component to prevent unnecessary rerenders
const ActivityCard = memo(
  ({
    activity,
    params,
    onLike,
    onComment,
    onShare,
    onPostClick,
  }: {
    activity: EnrichedActivityWithText;
    params: FeedModuleProps["params"];
    onLike: (activityId: string, isLiked: boolean, reactionId?: string) => void;
    onComment: (activityId: string, text: string) => void;
    onShare: (activityId: string) => void;
    onPostClick: (activityId: string) => void;
  }) => {
    const [showComments, setShowComments] = useState(false);
    const [commentText, setCommentText] = useState("");
    const [shareDropdownOpen, setShareDropdownOpen] = useState(false);
    const [imageViewerOpen, setImageViewerOpen] = useState(false);
    const [selectedImageUrl, setSelectedImageUrl] = useState<string>("");
    const [selectedImageAlt, setSelectedImageAlt] = useState<string>("");

    const isLiked = (activity.own_reactions?.like?.length || 0) > 0;
    const likeReactionId = activity.own_reactions?.like?.[0]?.id;

    // Use the comment loading hook
    const {
      data: comments,
      isLoading: commentsLoading,
      refetch: loadComments,
    } = useFeedComments(activity.id);

    const handleLike = useCallback(() => {
      onLike(activity.id, isLiked, likeReactionId);
    }, [activity.id, isLiked, likeReactionId, onLike]);

    const handleComment = useCallback(() => {
      if (commentText.trim()) {
        onComment(activity.id, commentText);
        setCommentText("");
      }
    }, [activity.id, commentText, onComment]);

    const handleShare = useCallback(() => {
      onShare(activity.id);
      setShareDropdownOpen(false);
    }, [activity.id, onShare]);

    const handleImageClick = useCallback((imageUrl: string, alt: string) => {
      setSelectedImageUrl(imageUrl);
      setSelectedImageAlt(alt);
      setImageViewerOpen(true);
    }, []);

    const handleCloseImageViewer = useCallback(() => {
      setImageViewerOpen(false);
      setSelectedImageUrl("");
      setSelectedImageAlt("");
    }, []);

    const formatTime = (timestamp: string) => {
      const date = dayjs.utc(timestamp).local();
      const now = dayjs();
      const diffInDays = now.diff(date, "day");

      if (diffInDays > 7) {
        return date.format("MMM D, YYYY");
      }

      return date.fromNow();
    };

    const getActorName = (actor: any): string => {
      if (typeof actor === "string") return actor;
      if (actor?.data?.name) return actor.data.name;
      if (actor?.id) return actor.id;
      return "Unknown User";
    };

    const getActorInitial = (actor: any): string => {
      const name = getActorName(actor);
      return name[0]?.toUpperCase() || "U";
    };

    const getActorImage = (actor: any): string | null => {
      if (actor?.data?.image) return actor.data.image;
      return null;
    };

    const getActivityContent = (activity: EnrichedActivityWithText): string => {
      if (activity.message) return activity.message;
      if (activity.text) return activity.text;
      if (typeof activity.object === "string") return activity.object;
      const obj = activity.object as any;
      if (obj?.text) return obj.text;
      if (obj?.content) return obj.content;
      if (obj?.id) return `${activity.verb} ${obj.id}`;
      return `${activity.verb}`;
    };

    return (
      <div
        className="bg-zinc-900 rounded-lg shadow-sm border border-zinc-700 p-6 cursor-pointer hover:bg-zinc-800/50 transition-colors relative"
        onClick={() => onPostClick(activity.id)}
      >
        {/* Activity Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            {getActorImage(activity.actor) ? (
              <img
                src={getActorImage(activity.actor)!}
                alt={getActorName(activity.actor)}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">
                  {getActorInitial(activity.actor)}
                </span>
              </div>
            )}
            <div>
              <p className="font-medium text-white">
                {getActorName(activity.actor)}
              </p>
              <p className="text-sm text-zinc-400">
                {formatTime(activity.time)}
              </p>
            </div>
          </div>
        </div>

        {/* Activity Content */}
        <div className="mb-4">
          <p className="text-zinc-200">{getActivityContent(activity)}</p>

          {/* Display legacy image field */}
          {activity.image && (
            <div className="mt-3 relative group cursor-pointer">
              <img
                src={activity.image}
                alt="Activity image"
                className="rounded-lg max-w-full transition-opacity group-hover:opacity-90"
                onClick={(e) => {
                  e.stopPropagation();
                  handleImageClick(activity.image!, "Activity image");
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded-lg">
                <ZoomIn className="w-8 h-8 text-white" />
              </div>
            </div>
          )}

          {/* Display attachments */}
          {activity.attachments && activity.attachments.length > 0 && (
            <div className="mt-3 space-y-2">
              {activity.attachments.map((attachment, index) => (
                <div key={index}>
                  {attachment.type === "image" && attachment.image_url && (
                    <div className="relative group cursor-pointer">
                      <img
                        src={attachment.image_url}
                        alt="Attached image"
                        className="rounded-lg max-w-full transition-opacity group-hover:opacity-90"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleImageClick(
                            attachment.image_url!,
                            "Attached image"
                          );
                        }}
                      />
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded-lg">
                        <ZoomIn className="w-8 h-8 text-white" />
                      </div>
                    </div>
                  )}
                  {attachment.type === "file" && attachment.asset_url && (
                    <a
                      href={attachment.asset_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-3 py-2 bg-zinc-100 rounded-lg hover:bg-zinc-200 transition-colors"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <span className="text-sm text-zinc-700">
                        📎 View File
                      </span>
                    </a>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Activity Actions */}
        <div className="flex items-center gap-6 pt-4 border-t border-zinc-700 relative z-10">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleLike();
            }}
            className={`flex items-center gap-2 transition-colors ${
              isLiked
                ? "text-blue-400 hover:text-blue-300"
                : "text-zinc-400 hover:text-blue-400"
            }`}
          >
            <Heart className={`w-5 h-5 ${isLiked ? "fill-current" : ""}`} />
            <span className="text-sm">
              {activity.reaction_counts?.like || 0}
            </span>
          </button>

          <button
            onClick={(e) => {
              e.stopPropagation();
              const newShowComments = !showComments;
              setShowComments(newShowComments);

              // Load comments if we're showing them and they haven't been loaded yet
              if (newShowComments && !comments) {
                loadComments();
              }
            }}
            className="flex items-center gap-2 text-zinc-400 hover:text-blue-400 transition-colors"
          >
            <MessageSquare className="w-5 h-5" />
            <span className="text-sm">
              {activity.reaction_counts?.comment || 0}
            </span>
          </button>

          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShareDropdownOpen(!shareDropdownOpen);
              }}
              className="flex items-center gap-2 text-zinc-400 hover:text-blue-400 transition-colors"
            >
              <Share2 className="w-5 h-5" />
              <span className="text-sm">Share</span>
            </button>

            {shareDropdownOpen && (
              <div className="absolute top-full left-0 mt-2 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg py-1 min-w-[140px] z-10">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleShare();
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-zinc-200 hover:bg-zinc-700 transition-colors flex items-center gap-2"
                >
                  <Link className="w-4 h-4" />
                  Copy Link
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Comments Section */}
        {showComments && (
          <div
            className="mt-4 pt-4 border-t border-zinc-700 relative z-10"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Comment Input */}
            <div className="flex gap-2 mb-4">
              <input
                type="text"
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleComment();
                  }
                }}
                placeholder="Write a comment..."
                className="flex-1 px-3 py-2 text-white bg-zinc-700 border border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-zinc-400"
              />
              <button
                onClick={handleComment}
                disabled={!commentText.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>

            {/* Comments Loading State */}
            {commentsLoading && (
              <div className="flex justify-center py-4">
                <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
              </div>
            )}

            {/* Show loaded comments */}
            {comments && comments.length > 0 && (
              <div className="space-y-3">
                {comments.map((comment: any) => (
                  <div key={comment.id} className="flex gap-3">
                    {comment.user?.data?.image ? (
                      <img
                        src={comment.user.data.image}
                        alt={comment.user.data.name || "User"}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
                        <span className="text-zinc-300 text-sm font-medium">
                          {(comment.user?.data?.name || "U")[0].toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="bg-zinc-700 rounded-lg px-3 py-2">
                        <p className="text-sm font-medium text-white">
                          {comment.user?.data?.name || "Anonymous"}
                        </p>
                        <p className="text-sm text-zinc-300">
                          {comment.data?.text || String(comment.data)}
                        </p>
                      </div>
                      <p className="text-xs text-zinc-500 mt-1">
                        {formatTime(comment.created_at)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Show recent comments from the activity if no full comments loaded */}
            {!commentsLoading &&
              !comments &&
              (activity as any).latest_reactions?.comment && (
                <div className="space-y-3">
                  {(activity as any).latest_reactions.comment.map(
                    (comment: any) => (
                      <div key={comment.id} className="flex gap-3">
                        {comment.user?.data?.image ? (
                          <img
                            src={comment.user.data.image}
                            alt={comment.user.data.name || "User"}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
                            <span className="text-zinc-300 text-sm font-medium">
                              {(comment.user?.data?.name ||
                                "U")[0].toUpperCase()}
                            </span>
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="bg-zinc-700 rounded-lg px-3 py-2">
                            <p className="text-sm font-medium text-white">
                              {comment.user?.data?.name || "Anonymous"}
                            </p>
                            <p className="text-sm text-zinc-300">
                              {comment.data?.text || String(comment.data)}
                            </p>
                          </div>
                          <p className="text-xs text-zinc-500 mt-1">
                            {formatTime(comment.created_at)}
                          </p>
                        </div>
                      </div>
                    )
                  )}
                </div>
              )}

            {/* Empty state */}
            {!commentsLoading &&
              !comments &&
              !(activity as any).latest_reactions?.comment && (
                <p className="text-sm text-zinc-400 text-center py-2">
                  No comments yet. Be the first to comment!
                </p>
              )}
          </div>
        )}

        {/* Image Viewer Modal */}
        <ImageViewer
          imageUrl={selectedImageUrl}
          alt={selectedImageAlt}
          isOpen={imageViewerOpen}
          onClose={handleCloseImageViewer}
        />
      </div>
    );
  }
);

ActivityCard.displayName = "ActivityCard";

export function FeedModule({ module, params }: FeedModuleProps) {
  const { data: profileData } = useProfile();
  const getUploadUrl = useGetUploadUrl();
  const navigate = useNavigate();

  // Use optimized React Query hooks
  const {
    data,
    error,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useFeedActivities(module.config.feedGroup, module.config.feedId);

  // Set up real-time feed subscription
  const { isConnected: isRealtimeConnected, lastUpdate } = useFeedRealtime(
    module.config.feedGroup,
    module.config.feedId
  );

  // Log real-time connection status for debugging
  useEffect(() => {
    console.log(
      `🔄 Real-time connection status: ${
        isRealtimeConnected ? "Connected" : "Disconnected"
      }`
    );
    if (lastUpdate) {
      console.log(
        `📅 Last update received at: ${lastUpdate.toLocaleTimeString()}`
      );
    }
  }, [isRealtimeConnected, lastUpdate]);

  const likeMutation = useFeedLike(
    module.config.feedGroup,
    module.config.feedId
  );
  const commentMutation = useFeedComment(
    module.config.feedGroup,
    module.config.feedId
  );
  const postMutation = useFeedPost(
    module.config.feedGroup,
    module.config.feedId
  );

  const [postText, setPostText] = useState("");
  const [postImage, setPostImage] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [mainImageViewerOpen, setMainImageViewerOpen] = useState(false);
  const [mainSelectedImageUrl, setMainSelectedImageUrl] = useState<string>("");
  const [mainSelectedImageAlt, setMainSelectedImageAlt] = useState<string>("");
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Check if user has permission to post
  const userRole = profileData?.data?.role;
  const userOrgId = profileData?.data?.organizationId;
  const canPost = canUserPostToFeed(userRole, userOrgId, module.config.feedId);

  // Get flattened activities from paginated data
  const activities = data?.pages.flatMap((page: any) => page.results) || [];

  // Infinite scroll handler
  const handleScroll = useCallback(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);

    if (distanceFromBottom <= 200 && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  // Attach scroll listener
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    scrollContainer.addEventListener("scroll", handleScroll, { passive: true });
    return () => scrollContainer.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  const handleLike = useCallback(
    (activityId: string, isLiked: boolean, reactionId?: string) => {
      likeMutation.mutate({ activityId, isLiked, reactionId });
    },
    [likeMutation]
  );

  const handleComment = useCallback(
    (activityId: string, text: string) => {
      commentMutation.mutate({ activityId, text });
    },
    [commentMutation]
  );

  const handleShare = useCallback(
    async (activityId: string) => {
      const postUrl = `${window.location.origin}/groups/${params.groupId}/cohorts/${params.cohortId}/modules/${params.moduleId}/posts/${activityId}`;

      try {
        await navigator.clipboard.writeText(postUrl);
        setShowToast(true);
        setTimeout(() => setShowToast(false), 3000);
      } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = postUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        setShowToast(true);
        setTimeout(() => setShowToast(false), 3000);
      }
    },
    [params]
  );

  const handlePostClick = useCallback(
    (activityId: string) => {
      navigate(
        `/groups/${params.groupId}/cohorts/${params.cohortId}/modules/${params.moduleId}/posts/${activityId}`
      );
    },
    [navigate, params]
  );

  const handleMainImageClick = useCallback((imageUrl: string, alt: string) => {
    setMainSelectedImageUrl(imageUrl);
    setMainSelectedImageAlt(alt);
    setMainImageViewerOpen(true);
  }, []);

  const handleCloseMainImageViewer = useCallback(() => {
    setMainImageViewerOpen(false);
    setMainSelectedImageUrl("");
    setMainSelectedImageAlt("");
  }, []);

  const handleCreatePost = useCallback(async () => {
    if (!postText.trim()) return;

    try {
      let attachment = undefined;

      if (selectedFile) {
        const uploadUrlResponse = await getUploadUrl.mutateAsync({
          contentType: selectedFile.type,
          fileName: selectedFile.name,
        });

        const uploadResponse = await fetch(uploadUrlResponse.uploadUrl, {
          method: "PUT",
          headers: {
            "Content-Type": selectedFile.type,
          },
          body: selectedFile,
        });

        if (!uploadResponse.ok) {
          throw new Error(`Upload failed: ${uploadResponse.statusText}`);
        }

        attachment = {
          type: isImageFile(selectedFile) ? "image" : "file",
          url: uploadUrlResponse.cdnUrl,
          fileName: selectedFile.name,
        };
      }

      await postMutation.mutateAsync({
        text: postText,
        attachment,
      });

      // Clear form
      setPostText("");
      setPostImage(null);
      setSelectedFile(null);
    } catch (error) {
      console.error("Error creating post:", error);
    }
  }, [postText, selectedFile, getUploadUrl, postMutation]);

  const handleImageUpload = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        if (!isImageFile(file)) {
          return;
        }

        const maxSize = 100 * 1024 * 1024; // 100MB
        if (file.size > maxSize) {
          return;
        }

        setSelectedFile(file);

        const reader = new FileReader();
        reader.onloadend = () => {
          setPostImage(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
      e.target.value = "";
    },
    []
  );

  return (
    <div className="flex flex-col h-full bg-black">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-900">
        <div className="max-w-4xl mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-white">
                {module.name}
              </h1>
              <div className="flex items-center gap-2">
                <p className="text-sm text-zinc-400">
                  {activities.length} Activities
                </p>
                {!isRealtimeConnected && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-xs text-red-400">Offline</span>
                  </div>
                )}
              </div>
            </div>
            <button className="p-2 hover:bg-zinc-900 rounded-lg transition-colors">
              <MoreHorizontal className="w-5 h-5 text-zinc-400" />
            </button>
          </div>
        </div>
      </div>

      {/* Feed Content */}
      <div ref={scrollContainerRef} className="flex-1 overflow-auto bg-black">
        <div className="max-w-4xl mx-auto px-8 py-12">
          {/* Create Post Form */}
          {!isLoading && !error && canPost && (
            <div className="bg-zinc-900 rounded-lg shadow-sm border border-zinc-700 p-6 mb-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                Create a Post
              </h3>

              <textarea
                value={postText}
                onChange={(e) => setPostText(e.target.value)}
                placeholder="What's on your mind?"
                className="w-full px-4 py-3 text-white bg-zinc-700 border border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none placeholder-zinc-400"
                rows={3}
              />

              {postImage && (
                <div className="mt-3 relative group">
                  <img
                    src={postImage}
                    alt="Upload preview"
                    className="max-h-64 rounded-lg cursor-pointer transition-opacity group-hover:opacity-90"
                    onClick={() =>
                      handleMainImageClick(postImage, "Upload preview")
                    }
                  />
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded-lg pointer-events-none">
                    <ZoomIn className="w-8 h-8 text-white" />
                  </div>
                  <button
                    onClick={() => {
                      setPostImage(null);
                      setSelectedFile(null);
                    }}
                    className="absolute top-2 right-2 p-1 bg-black/50 rounded-full hover:bg-black/70 transition-colors z-10"
                  >
                    <X className="w-4 h-4 text-white" />
                  </button>
                </div>
              )}

              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <label className="cursor-pointer">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <div className="p-2 text-zinc-400 hover:bg-zinc-700 rounded-lg transition-colors">
                      <ImageIcon className="w-5 h-5" />
                    </div>
                  </label>
                </div>

                <button
                  onClick={handleCreatePost}
                  disabled={!postText.trim() || postMutation.isPending}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  {postMutation.isPending ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      {selectedFile ? "Uploading & Posting..." : "Posting..."}
                    </>
                  ) : (
                    "Post"
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-300">
              Failed to load feed. Please try again later.
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !error && activities.length === 0 && (
            <div className="text-center py-12">
              <p className="text-zinc-400">
                No activities yet. Check back later!
              </p>
            </div>
          )}

          {/* Activities */}
          {!isLoading && !error && activities.length > 0 && (
            <div className="space-y-4">
              {activities.map((activity) => (
                <ActivityCard
                  key={activity.id}
                  activity={activity}
                  params={params}
                  onLike={handleLike}
                  onComment={handleComment}
                  onShare={handleShare}
                  onPostClick={handlePostClick}
                />
              ))}
            </div>
          )}

          {/* Loading More */}
          {isFetchingNextPage && (
            <div className="flex justify-center py-8">
              <div className="flex items-center gap-2 text-zinc-400">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Loading more activities...</span>
              </div>
            </div>
          )}

          {/* End of Feed */}
          {!isLoading && !error && activities.length > 0 && !hasNextPage && (
            <div className="flex justify-center py-8">
              <div className="text-zinc-500 text-sm">
                You've reached the end of the feed
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 bg-zinc-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <Check className="w-4 h-4" />
          <p className="text-xs">Link copied to clipboard!</p>
        </div>
      )}

      {/* Main Image Viewer Modal */}
      <ImageViewer
        imageUrl={mainSelectedImageUrl}
        alt={mainSelectedImageAlt}
        isOpen={mainImageViewerOpen}
        onClose={handleCloseMainImageViewer}
      />
    </div>
  );
}
